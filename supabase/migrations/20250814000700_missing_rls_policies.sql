-- RatioHub Missing RLS Policies Migration
-- This migration adds RLS policies for all new tables

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.ticket_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- TICKET RESPONSES POLICIES
-- ============================================================================

CREATE POLICY "Users can view responses for tickets they have access to" 
ON public.ticket_responses FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.support_tickets st
    WHERE st.id = ticket_responses.ticket_id
    AND (
      st.created_by = auth.uid() OR
      st.assigned_to = auth.uid() OR
      is_admin(auth.uid()) OR
      is_project_manager(auth.uid())
    )
  )
);

CREATE POLICY "Users can create responses for tickets they have access to" 
ON public.ticket_responses FOR INSERT 
WITH CHECK (
  user_id = auth.uid() AND
  EXISTS (
    SELECT 1 FROM public.support_tickets st
    WHERE st.id = ticket_responses.ticket_id
    AND (
      st.created_by = auth.uid() OR
      st.assigned_to = auth.uid() OR
      is_admin(auth.uid()) OR
      is_project_manager(auth.uid())
    )
  )
);

CREATE POLICY "Users can update their own responses" 
ON public.ticket_responses FOR UPDATE 
USING (user_id = auth.uid()) 
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Only admins can delete responses" 
ON public.ticket_responses FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- USER INVITATIONS POLICIES
-- ============================================================================

CREATE POLICY "Admins and project managers can view all invitations" 
ON public.user_invitations FOR SELECT 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Admins and project managers can create invitations" 
ON public.user_invitations FOR INSERT 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Admins and project managers can update invitations" 
ON public.user_invitations FOR UPDATE 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Only admins can delete invitations" 
ON public.user_invitations FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- INVOICES POLICIES
-- ============================================================================

CREATE POLICY "Users can view invoices for their projects or as clients" 
ON public.invoices FOR SELECT 
USING (
  is_admin(auth.uid()) OR
  is_project_manager(auth.uid()) OR
  client_id = auth.uid() OR
  has_project_access(auth.uid(), project_id)
);

CREATE POLICY "Admins and project managers can create invoices" 
ON public.invoices FOR INSERT 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Admins and project managers can update invoices" 
ON public.invoices FOR UPDATE 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Only admins can delete invoices" 
ON public.invoices FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- INVOICE ITEMS POLICIES
-- ============================================================================

CREATE POLICY "Users can view invoice items for invoices they can access" 
ON public.invoice_items FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.invoices i
    WHERE i.id = invoice_items.invoice_id
    AND (
      is_admin(auth.uid()) OR
      is_project_manager(auth.uid()) OR
      i.client_id = auth.uid() OR
      has_project_access(auth.uid(), i.project_id)
    )
  )
);

CREATE POLICY "Admins and project managers can manage invoice items" 
ON public.invoice_items FOR ALL 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid())) 
WITH CHECK (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

-- ============================================================================
-- SYSTEM SETTINGS POLICIES
-- ============================================================================

CREATE POLICY "Everyone can view public system settings" 
ON public.system_settings FOR SELECT 
USING (is_public = true OR is_admin(auth.uid()));

CREATE POLICY "Only admins can manage system settings" 
ON public.system_settings FOR INSERT 
WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can update system settings" 
ON public.system_settings FOR UPDATE 
USING (is_admin(auth.uid())) 
WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can delete system settings" 
ON public.system_settings FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- EMAIL TEMPLATES POLICIES
-- ============================================================================

CREATE POLICY "Admins and project managers can view email templates" 
ON public.email_templates FOR SELECT 
USING (is_admin(auth.uid()) OR is_project_manager(auth.uid()));

CREATE POLICY "Only admins can manage email templates" 
ON public.email_templates FOR INSERT 
WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can update email templates" 
ON public.email_templates FOR UPDATE 
USING (is_admin(auth.uid())) 
WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can delete email templates" 
ON public.email_templates FOR DELETE 
USING (is_admin(auth.uid()));

-- ============================================================================
-- INSERT DEFAULT DATA
-- ============================================================================

-- Insert default system settings
INSERT INTO public.system_settings (key, value, category, description, is_public) VALUES
('company_name', 'RatioHub', 'general', 'Company name displayed in the application', true),
('default_currency', 'USD', 'billing', 'Default currency for billing and invoices', true),
('tax_rate', '0.10', 'billing', 'Default tax rate for invoices (10%)', false),
('email_notifications', 'true', 'notifications', 'Enable email notifications', false),
('auto_backup', 'true', 'backup', 'Enable automatic daily backups', false),
('default_hourly_rate', '125', 'billing', 'Default hourly rate for new projects', false),
('invoice_due_days', '30', 'billing', 'Default number of days for invoice due date', false),
('max_file_size_mb', '10', 'files', 'Maximum file upload size in MB', true),
('allowed_file_types', 'pdf,doc,docx,xls,xlsx,png,jpg,jpeg,gif', 'files', 'Allowed file types for uploads', true),
('session_timeout_hours', '24', 'security', 'Session timeout in hours', false)
ON CONFLICT (key) DO NOTHING;

-- Insert default email templates
INSERT INTO public.email_templates (name, subject, body, variables, is_active) VALUES
('user_invitation', 'You''re invited to join {{company_name}}', 
'Hello,

You have been invited to join {{company_name}} as a {{role}}.

Please click the link below to accept your invitation:
{{invitation_link}}

{{#personal_message}}
Personal message from {{inviter_name}}:
{{personal_message}}
{{/personal_message}}

Best regards,
The {{company_name}} Team', 
'{"company_name": "Company name", "role": "User role", "invitation_link": "Invitation acceptance link", "inviter_name": "Name of person who sent invitation", "personal_message": "Optional personal message"}', 
true),

('project_assignment', 'You''ve been assigned to project: {{project_name}}', 
'Hello {{user_name}},

You have been assigned to the project "{{project_name}}".

Project Details:
- Project ID: {{project_id}}
- Start Date: {{start_date}}
- Description: {{project_description}}

You can access the project dashboard here: {{project_link}}

Best regards,
The {{company_name}} Team', 
'{"user_name": "User full name", "project_name": "Project name", "project_id": "Project ID", "start_date": "Project start date", "project_description": "Project description", "project_link": "Link to project", "company_name": "Company name"}', 
true),

('task_assignment', 'New task assigned: {{task_title}}', 
'Hello {{user_name}},

A new task has been assigned to you:

Task: {{task_title}}
Project: {{project_name}}
Due Date: {{due_date}}
Priority: {{priority}}

Description:
{{task_description}}

View task: {{task_link}}

Best regards,
The {{company_name}} Team', 
'{"user_name": "User full name", "task_title": "Task title", "project_name": "Project name", "due_date": "Task due date", "priority": "Task priority", "task_description": "Task description", "task_link": "Link to task", "company_name": "Company name"}', 
true),

('invoice_sent', 'Invoice {{invoice_number}} - {{company_name}}', 
'Dear {{client_name}},

Please find attached invoice {{invoice_number}} for project "{{project_name}}".

Invoice Details:
- Invoice Number: {{invoice_number}}
- Issue Date: {{issue_date}}
- Due Date: {{due_date}}
- Amount: {{total_amount}} {{currency}}

You can view and pay this invoice online: {{invoice_link}}

Thank you for your business!

Best regards,
The {{company_name}} Team', 
'{"client_name": "Client name", "invoice_number": "Invoice number", "project_name": "Project name", "issue_date": "Invoice issue date", "due_date": "Invoice due date", "total_amount": "Total amount", "currency": "Currency", "invoice_link": "Link to invoice", "company_name": "Company name"}', 
true)
ON CONFLICT (name) DO NOTHING;
