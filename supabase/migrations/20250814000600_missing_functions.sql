-- RatioHub Missing Functions Migration
-- This migration adds all missing functions identified in the integration analysis

-- ============================================================================
-- MISSING RPC FUNCTIONS
-- ============================================================================

-- Function to create admin user (referenced in AdminUserManagement.tsx)
CREATE OR REPLACE FUNCTION public.create_admin_user(user_email text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    user_record public.profiles%ROWTYPE;
BEGIN
    -- Find user by email
    SELECT * INTO user_record FROM public.profiles WHERE email = user_email;
    
    -- Return false if user not found
    IF user_record.user_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Add admin role (ON CONFLICT DO NOTHING prevents duplicates)
    INSERT INTO public.user_roles (user_id, role, assigned_by)
    VALUES (user_record.user_id, 'admin', auth.uid())
    ON CONFLICT (user_id, role) DO NOTHING;
    
    RETURN true;
END;
$$;

-- Function to assign default role to new users
CREATE OR REPLACE FUNCTION public.assign_default_role(user_uuid uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    -- Assign 'user' role as default for new users
    INSERT INTO public.user_roles (user_id, role)
    VALUES (user_uuid, 'user')
    ON CONFLICT (user_id, role) DO NOTHING;
END;
$$;

-- Function to generate invoice number
CREATE OR REPLACE FUNCTION public.generate_invoice_number()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    current_year TEXT;
    next_number INTEGER;
    new_invoice_number TEXT;
BEGIN
    current_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    -- Find highest existing invoice number for current year
    SELECT COALESCE(MAX(CAST(substring(invoice_number, 10) AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.invoices
    WHERE invoice_number ~ ('^INV-' || current_year || '-[0-9]{3}$');
    
    -- Generate new invoice number: INV-YYYY-001
    new_invoice_number := 'INV-' || current_year || '-' || lpad(next_number::TEXT, 3, '0');
    
    RETURN new_invoice_number;
END;
$$;

-- Function to calculate invoice totals
CREATE OR REPLACE FUNCTION public.calculate_invoice_totals(invoice_uuid uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    subtotal_amount NUMERIC;
    tax_rate NUMERIC := 0.10; -- 10% tax rate (configurable via system_settings)
    tax_amount NUMERIC;
    total_amount NUMERIC;
BEGIN
    -- Calculate subtotal from invoice items
    SELECT COALESCE(SUM(amount), 0)
    INTO subtotal_amount
    FROM public.invoice_items
    WHERE invoice_id = invoice_uuid;
    
    -- Get tax rate from system settings (fallback to 10%)
    SELECT COALESCE(CAST(value AS NUMERIC), 0.10)
    INTO tax_rate
    FROM public.system_settings
    WHERE key = 'tax_rate'
    LIMIT 1;
    
    -- Calculate tax and total
    tax_amount := subtotal_amount * tax_rate;
    total_amount := subtotal_amount + tax_amount;
    
    -- Update invoice with calculated totals
    UPDATE public.invoices
    SET 
        subtotal = subtotal_amount,
        tax_amount = tax_amount,
        total_amount = total_amount,
        updated_at = now()
    WHERE id = invoice_uuid;
END;
$$;

-- Function to update milestone progress based on task completion
CREATE OR REPLACE FUNCTION public.update_milestone_progress(milestone_uuid uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    total_tasks INTEGER;
    completed_tasks INTEGER;
    completion_percentage NUMERIC;
BEGIN
    -- Count total and completed tasks for this milestone
    SELECT 
        COUNT(*),
        COUNT(CASE WHEN status = 'completed' THEN 1 END)
    INTO total_tasks, completed_tasks
    FROM public.tasks
    WHERE milestone_id = milestone_uuid;
    
    -- Calculate completion percentage
    IF total_tasks > 0 THEN
        completion_percentage := (completed_tasks::NUMERIC / total_tasks::NUMERIC) * 100;
        
        -- Update milestone completion status
        UPDATE public.milestones
        SET 
            is_completed = (completion_percentage = 100),
            completed_at = CASE 
                WHEN completion_percentage = 100 AND is_completed = false THEN now()
                WHEN completion_percentage < 100 THEN NULL
                ELSE completed_at
            END,
            updated_at = now()
        WHERE id = milestone_uuid;
    END IF;
END;
$$;

-- Function to update task actual hours from time entries
CREATE OR REPLACE FUNCTION public.update_task_actual_hours(task_uuid uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    total_minutes INTEGER;
    total_hours NUMERIC;
BEGIN
    -- Calculate total minutes from time entries
    SELECT COALESCE(SUM(duration_minutes), 0)
    INTO total_minutes
    FROM public.time_entries
    WHERE task_id = task_uuid AND end_time IS NOT NULL;
    
    -- Convert to hours
    total_hours := total_minutes::NUMERIC / 60;
    
    -- Update task actual hours
    UPDATE public.tasks
    SET 
        actual_hours = total_hours,
        updated_at = now()
    WHERE id = task_uuid;
END;
$$;

-- ============================================================================
-- ENHANCED TRIGGER FUNCTIONS
-- ============================================================================

-- Enhanced user creation handler with default role assignment
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    -- Create profile
    INSERT INTO public.profiles (user_id, email, first_name, last_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'last_name', '')
    );
    
    -- Assign default role
    PERFORM public.assign_default_role(NEW.id);
    
    RETURN NEW;
END;
$$;

-- Function to auto-generate invoice number on insert
CREATE OR REPLACE FUNCTION public.set_invoice_number()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_number := public.generate_invoice_number();
    END IF;
    RETURN NEW;
END;
$$;

-- ============================================================================
-- NEW TRIGGERS
-- ============================================================================

-- Trigger for invoice number generation
CREATE TRIGGER set_invoice_number_trigger 
    BEFORE INSERT ON public.invoices 
    FOR EACH ROW EXECUTE FUNCTION public.set_invoice_number();

-- Trigger for invoice total calculation
CREATE TRIGGER calculate_invoice_totals_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON public.invoice_items 
    FOR EACH ROW EXECUTE FUNCTION public.calculate_invoice_totals(
        COALESCE(NEW.invoice_id, OLD.invoice_id)
    );

-- Trigger for milestone progress update
CREATE TRIGGER update_milestone_progress_trigger 
    AFTER UPDATE OF status ON public.tasks 
    FOR EACH ROW 
    WHEN (NEW.milestone_id IS NOT NULL)
    EXECUTE FUNCTION public.update_milestone_progress(NEW.milestone_id);

-- Trigger for task actual hours update
CREATE TRIGGER update_task_actual_hours_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON public.time_entries 
    FOR EACH ROW 
    WHEN (COALESCE(NEW.task_id, OLD.task_id) IS NOT NULL)
    EXECUTE FUNCTION public.update_task_actual_hours(
        COALESCE(NEW.task_id, OLD.task_id)
    );

-- Update triggers for new tables
CREATE TRIGGER update_ticket_responses_updated_at 
    BEFORE UPDATE ON public.ticket_responses 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_invitations_updated_at 
    BEFORE UPDATE ON public.user_invitations 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at 
    BEFORE UPDATE ON public.invoices 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at 
    BEFORE UPDATE ON public.system_settings 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_email_templates_updated_at 
    BEFORE UPDATE ON public.email_templates 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
