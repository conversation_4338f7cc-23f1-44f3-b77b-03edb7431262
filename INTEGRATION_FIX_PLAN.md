# RatioHub Integration Fix Plan & Implementation Guide

## Overview

This document provides a comprehensive plan to fix all identified integration gaps in RatioHub, transforming it from 65% complete to production-ready. The plan is organized into 6 phases with specific implementation steps, code examples, and testing strategies.

## Quick Start - Critical Fixes (2-3 days)

### Step 1: Apply Database Migrations
```bash
# Apply the new migrations in order
supabase db push

# Or apply individually:
supabase migration up 20250814000500_missing_tables.sql
supabase migration up 20250814000600_missing_functions.sql
supabase migration up 20250814000700_missing_rls_policies.sql
```

### Step 2: Verify Database Changes
```sql
-- Check that all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('ticket_responses', 'user_invitations', 'invoices', 'invoice_items', 'system_settings', 'email_templates');

-- Check that functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('create_admin_user', 'generate_invoice_number', 'assign_default_role');
```

### Step 3: Test Critical Workflows
1. **Support Tickets**: Create ticket → Add response → Verify response appears
2. **User Invitations**: Send invitation → Check invitation table → Verify email function
3. **Admin Functions**: Use create_admin_user RPC → Verify role assignment

## PHASE 1: Critical Database Schema Fixes ✅ COMPLETE

**Status**: Database migrations created and ready to apply
**Files Created**:
- `supabase/migrations/20250814000500_missing_tables.sql`
- `supabase/migrations/20250814000600_missing_functions.sql`
- `supabase/migrations/20250814000700_missing_rls_policies.sql`

**What's Fixed**:
- ✅ Missing `ticket_responses` table (breaks support system)
- ✅ Missing `user_invitations` table (breaks invitation workflow)
- ✅ Missing `invoices` and `invoice_items` tables (enables billing persistence)
- ✅ Missing `system_settings` table (enables configuration)
- ✅ Missing `create_admin_user` RPC function
- ✅ Enhanced user creation with automatic role assignment
- ✅ Auto-calculation triggers for invoices and milestones

## PHASE 2: Business Logic Validation Implementation

### 2.1: Fix Client Role Validation in Project Assignment

**Problem**: ProjectCreateSheet shows all profiles as potential clients, not filtered by role.

**Solution**: Update the client query to only show users with 'client' role.

```typescript
// In ProjectCreateSheet.tsx - Update the clients query
const { data: clients } = useQuery({
  queryKey: ['clients'],
  queryFn: async () => {
    try {
      const { data, error } = await (supabase as any)
        .from('profiles')
        .select(`
          user_id, 
          first_name, 
          last_name, 
          email,
          user_roles!inner(role)
        `)
        .eq('user_roles.role', 'client')
        .eq('is_active', true)
        .order('first_name');
      
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.warn('Clients query failed:', error);
      return [];
    }
  },
});
```

### 2.2: Fix Task Assignment to Project Team Members Only

**Problem**: Task assignment dropdown may show all team members, not filtered to project team.

**Solution**: Update TaskCreateSheet to filter assignees by project team.

```typescript
// In TaskCreateSheet.tsx - Add project team members query
const { data: projectTeamMembers } = useQuery({
  queryKey: ['project-team-members', projectId],
  queryFn: async () => {
    try {
      const { data, error } = await (supabase as any)
        .from('projects')
        .select(`
          team_id,
          teams!inner(
            team_members!inner(
              user_id,
              profiles!inner(
                user_id,
                first_name,
                last_name,
                email
              )
            )
          )
        `)
        .eq('id', projectId)
        .single();
      
      if (error) throw error;
      return data?.teams?.team_members?.map(tm => tm.profiles) || [];
    } catch (error) {
      console.warn('Project team members query failed:', error);
      return [];
    }
  },
  enabled: !!projectId,
});
```

### 2.3: Add Time Entry Validation

**Problem**: No validation that selected task belongs to selected project.

**Solution**: Add validation in TimeTracker component.

```typescript
// In TimeTracker.tsx - Add validation before starting timer
const validateTimeEntry = async () => {
  if (!selectedProject || !selectedTask) return true;
  
  const { data, error } = await (supabase as any)
    .from('tasks')
    .select('project_id')
    .eq('id', selectedTask)
    .single();
    
  if (error || data?.project_id !== selectedProject) {
    toast({
      title: 'Invalid Selection',
      description: 'Selected task does not belong to the selected project.',
      variant: 'destructive',
    });
    return false;
  }
  return true;
};

// Update startTimerMutation
const startTimerMutation = useMutation({
  mutationFn: async () => {
    if (!selectedProject) throw new Error('Please select a project');
    
    // Add validation
    const isValid = await validateTimeEntry();
    if (!isValid) throw new Error('Invalid project/task combination');
    
    // ... rest of the mutation
  },
  // ... rest of the mutation
});
```

## PHASE 3: Complete Billing System Integration

### 3.1: Create Invoice Management Interface

**File**: `src/components/billing/InvoiceManagement.tsx`

```typescript
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const InvoiceManagement: React.FC = () => {
  const queryClient = useQueryClient();
  
  // Fetch real invoices from database
  const { data: invoices = [], isLoading } = useQuery({
    queryKey: ['invoices'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          projects(name, project_id),
          clients:profiles!client_id(first_name, last_name, email),
          invoice_items(*)
        `)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  // Create invoice from time entries
  const createInvoiceMutation = useMutation({
    mutationFn: async ({ projectId, timeEntryIds }: { projectId: string; timeEntryIds: string[] }) => {
      // Get project and time entries data
      const { data: project } = await supabase
        .from('projects')
        .select('*, profiles!client_id(*)')
        .eq('id', projectId)
        .single();
        
      const { data: timeEntries } = await supabase
        .from('time_entries')
        .select('*')
        .in('id', timeEntryIds);
      
      // Create invoice
      const { data: invoice, error: invoiceError } = await supabase
        .from('invoices')
        .insert({
          project_id: projectId,
          client_id: project.client_id,
          status: 'draft',
          currency: project.currency || 'USD',
          due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          created_by: (await supabase.auth.getUser()).data.user?.id,
        })
        .select()
        .single();
        
      if (invoiceError) throw invoiceError;
      
      // Create invoice items from time entries
      const invoiceItems = timeEntries?.map(entry => ({
        invoice_id: invoice.id,
        description: `Time tracking - ${entry.description || 'Work performed'}`,
        quantity: entry.duration_minutes / 60, // Convert to hours
        rate: entry.hourly_rate || project.hourly_rate || 125,
        amount: (entry.duration_minutes / 60) * (entry.hourly_rate || project.hourly_rate || 125),
        time_entry_ids: [entry.id],
      }));
      
      if (invoiceItems?.length) {
        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(invoiceItems);
          
        if (itemsError) throw itemsError;
      }
      
      return invoice;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast.success('Invoice created successfully');
    },
  });

  // ... rest of component
};
```

### 3.2: Update InvoiceManager to Use Real Data

Replace the mock data in `src/components/billing/InvoiceManager.tsx` with real database queries:

```typescript
// Replace the existing invoices query with:
const { data: invoices = [], isLoading } = useQuery({
  queryKey: ['invoices'],
  queryFn: async () => {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        projects(name, project_id),
        clients:profiles!client_id(first_name, last_name, email),
        invoice_items(*)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },
});
```

## PHASE 4: Real-time Collaboration Features

### 4.1: Add Supabase Real-time Subscriptions

**File**: `src/hooks/useRealtimeSubscription.ts`

```typescript
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useRealtimeSubscription = (table: string, queryKey: string[]) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const subscription = supabase
      .channel(`${table}_changes`)
      .on('postgres_changes', 
        { event: '*', schema: 'public', table }, 
        (payload) => {
          console.log(`${table} change:`, payload);
          queryClient.invalidateQueries({ queryKey });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [table, queryKey, queryClient]);
};
```

### 4.2: Add Real-time Updates to Key Components

```typescript
// In Dashboard.tsx
import { useRealtimeSubscription } from '@/hooks/useRealtimeSubscription';

export const Dashboard = () => {
  // Add real-time subscriptions
  useRealtimeSubscription('projects', ['dashboard-data']);
  useRealtimeSubscription('tasks', ['dashboard-data']);
  useRealtimeSubscription('time_entries', ['dashboard-data']);
  
  // ... rest of component
};

// In SupportTickets.tsx
export const SupportTickets = () => {
  useRealtimeSubscription('support_tickets', ['support-tickets']);
  useRealtimeSubscription('ticket_responses', ['ticket-responses']);
  
  // ... rest of component
};
```

### 4.3: Add Automatic Notification Creation

**File**: `src/utils/notifications.ts`

```typescript
import { supabase } from '@/integrations/supabase/client';

export const createNotification = async ({
  userId,
  title,
  message,
  type = 'info',
  entityType,
  entityId,
}: {
  userId: string;
  title: string;
  message: string;
  type?: string;
  entityType?: string;
  entityId?: string;
}) => {
  const { error } = await supabase
    .from('notifications')
    .insert({
      user_id: userId,
      title,
      message,
      type,
      entity_type: entityType,
      entity_id: entityId,
    });
    
  if (error) {
    console.error('Failed to create notification:', error);
  }
};

// Usage in task assignment
export const notifyTaskAssignment = async (taskId: string, assigneeId: string, taskTitle: string) => {
  await createNotification({
    userId: assigneeId,
    title: 'New Task Assigned',
    message: `You have been assigned to task: ${taskTitle}`,
    type: 'task_assignment',
    entityType: 'task',
    entityId: taskId,
  });
};
```

## PHASE 5: Performance Optimization & UX Improvements

### 5.1: Add Pagination to List Views

**File**: `src/hooks/usePagination.ts`

```typescript
import { useState } from 'react';

export const usePagination = (pageSize: number = 10) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  
  const offset = (currentPage - 1) * pageSize;
  const totalPages = Math.ceil(totalItems / pageSize);
  
  return {
    currentPage,
    setCurrentPage,
    totalItems,
    setTotalItems,
    pageSize,
    offset,
    totalPages,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1,
  };
};
```

### 5.2: Optimize Database Queries

```typescript
// Example: Optimize dashboard query to reduce N+1 problems
const { data: dashboardData } = useQuery({
  queryKey: ['dashboard-data'],
  queryFn: async () => {
    // Single query with joins instead of multiple queries
    const { data: projects } = await supabase
      .from('projects')
      .select(`
        *,
        tasks(count),
        time_entries(duration_minutes),
        team_members:teams!inner(team_members(count))
      `)
      .order('created_at', { ascending: false })
      .limit(5);
      
    // Calculate aggregates in the query
    const stats = projects?.reduce((acc, project) => ({
      totalProjects: acc.totalProjects + 1,
      totalTasks: acc.totalTasks + (project.tasks?.[0]?.count || 0),
      totalHours: acc.totalHours + (project.time_entries?.reduce((sum, entry) => 
        sum + (entry.duration_minutes || 0), 0) || 0) / 60,
    }), { totalProjects: 0, totalTasks: 0, totalHours: 0 });
    
    return { projects, stats };
  },
});
```

## PHASE 6: Advanced Features & Production Readiness

### 6.1: Email Notification System

**File**: `supabase/functions/send-email/index.ts`

```typescript
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const handler = async (req: Request): Promise<Response> => {
  const { templateName, recipientEmail, variables } = await req.json();
  
  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
  );
  
  // Get email template
  const { data: template } = await supabaseClient
    .from('email_templates')
    .select('*')
    .eq('name', templateName)
    .eq('is_active', true)
    .single();
    
  if (!template) {
    throw new Error(`Template ${templateName} not found`);
  }
  
  // Replace variables in template
  let subject = template.subject;
  let body = template.body;
  
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    subject = subject.replace(new RegExp(placeholder, 'g'), value as string);
    body = body.replace(new RegExp(placeholder, 'g'), value as string);
  });
  
  // Send email using your preferred service (Resend, SendGrid, etc.)
  // Implementation depends on your email service
  
  return new Response(JSON.stringify({ success: true }), {
    headers: { "Content-Type": "application/json" },
  });
};

serve(handler);
```

### 6.2: Advanced Reporting System

**File**: `src/components/reports/AdvancedReports.tsx`

```typescript
// Enhanced reporting with real data aggregation
const { data: reportData } = useQuery({
  queryKey: ['advanced-reports', dateRange, filters],
  queryFn: async () => {
    // Complex aggregation query
    const { data } = await supabase.rpc('generate_advanced_report', {
      start_date: dateRange.start,
      end_date: dateRange.end,
      project_ids: filters.projectIds,
      user_ids: filters.userIds,
    });
    
    return data;
  },
});
```

## Testing Strategy

### Unit Tests
```typescript
// Example test for invoice calculation
describe('Invoice Calculation', () => {
  it('should calculate correct totals', async () => {
    const invoice = await createInvoice({
      projectId: 'test-project',
      timeEntryIds: ['entry1', 'entry2'],
    });
    
    expect(invoice.subtotal).toBe(1000);
    expect(invoice.tax_amount).toBe(100);
    expect(invoice.total_amount).toBe(1100);
  });
});
```

### Integration Tests
```typescript
// Example integration test
describe('Project Creation Workflow', () => {
  it('should create project with team and generate tasks', async () => {
    // Create project
    const project = await createProject({
      name: 'Test Project',
      teamId: 'test-team',
      clientId: 'test-client',
    });
    
    // Verify project ID generation
    expect(project.project_id).toMatch(/^[A-Z]{2}\d{3}$/);
    
    // Create task
    const task = await createTask({
      projectId: project.id,
      title: 'Test Task',
    });
    
    // Verify task ID generation
    expect(task.task_id).toBe(`${project.project_id}-T001`);
  });
});
```

## Deployment Checklist

### Pre-deployment
- [ ] Run all database migrations
- [ ] Verify all tests pass
- [ ] Check RLS policies are working
- [ ] Test with realistic data volumes
- [ ] Verify email templates are configured

### Post-deployment
- [ ] Monitor error logs
- [ ] Verify real-time subscriptions work
- [ ] Test critical workflows end-to-end
- [ ] Check performance metrics
- [ ] Validate security policies

## Monitoring & Maintenance

### Key Metrics to Monitor
- Database query performance
- Real-time subscription connection count
- Email delivery rates
- User session duration
- Error rates by feature

### Regular Maintenance Tasks
- Clean up expired invitations
- Archive old notifications
- Backup invoice data
- Update email templates
- Review and optimize slow queries

This plan provides a complete roadmap to transform RatioHub from its current 65% completion to a production-ready project management system. Each phase builds on the previous one, ensuring a stable and scalable implementation.
