import { TaskDetailView } from '@/components/tasks/TaskDetailView';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { Calendar, ChevronRight, Clock, Plus, User } from 'lucide-react';
import React, { useState } from 'react';

interface ProjectTasksProps {
  projectId: string;
}

export const ProjectTasks: React.FC<ProjectTasksProps> = ({ projectId }) => {
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch tasks for the project
  const { data: tasks, isLoading, refetch } = useQuery({
    queryKey: ['project-tasks', projectId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          milestone:milestones(name)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Get assigned user profiles separately
      const tasksWithProfiles = await Promise.all(
        (data || []).map(async (task) => {
          let assigned_to_profile = null;
          if (task.assigned_to) {
            const { data: profile } = await supabase
              .from('profiles')
              .select('first_name, last_name')
              .eq('user_id', task.assigned_to)
              .single();
            assigned_to_profile = profile;
          }
          return { ...task, assigned_to_profile };
        })
      );

      return tasksWithProfiles;
    },
    enabled: !!projectId
  });

  // Fetch team members for assignment
  const { data: teamMembers } = useQuery({
    queryKey: ['project-team-members', projectId],
    queryFn: async () => {
      const { data: project } = await supabase
        .from('projects')
        .select('team_id')
        .eq('id', projectId)
        .single();

      if (!project?.team_id) return [];

      const { data: members } = await supabase
        .from('team_members')
        .select(`
          user_id,
          profiles!inner(first_name, last_name, user_id)
        `)
        .eq('team_id', project.team_id);

      return members?.map(m => m.profiles) || [];
    },
    enabled: !!projectId
  });

  // Update task status mutation
  const updateTaskMutation = useMutation({
    mutationFn: async ({ taskId, updates }: { taskId: string; updates: any }) => {
      const { error } = await supabase
        .from('tasks')
        .update(updates)
        .eq('id', taskId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({ title: 'Task updated successfully' });
      refetch();
    },
    onError: (error) => {
      toast({ 
        title: 'Error updating task', 
        description: error.message,
        variant: 'destructive' 
      });
    }
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in_progress': return 'bg-blue-500';
      case 'todo': return 'bg-gray-500';
      case 'on_hold': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  const handleStatusChange = (taskId: string, newStatus: string) => {
    updateTaskMutation.mutate({ 
      taskId, 
      updates: { status: newStatus }
    });
  };

  const handleAssignTask = (taskId: string, userId: string) => {
    updateTaskMutation.mutate({ 
      taskId, 
      updates: { assigned_to: userId || null }
    });
  };

  if (selectedTask) {
    return (
      <div className="space-y-4">
        <Button 
          variant="ghost" 
          onClick={() => setSelectedTask(null)}
          className="mb-4"
        >
          ← Back to Task List
        </Button>
        <TaskDetailView 
          taskId={selectedTask} 
          onClose={() => setSelectedTask(null)}
        />
      </div>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Project Tasks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>Loading tasks...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Project Tasks ({tasks?.length || 0})</CardTitle>
        <Button size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Task
        </Button>
      </CardHeader>
      <CardContent>
        {!tasks || tasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No tasks found for this project</p>
            <p className="text-sm mt-2">Create your first task to get started</p>
          </div>
        ) : (
          <div className="space-y-4">
            {tasks.map((task) => (
              <div
                key={task.id}
                className="border rounded-lg p-4 hover:bg-accent/50 cursor-pointer transition-colors"
                onClick={() => {
                  console.log('🔍 TASK CLICKED:', {
                    id: task.id,
                    task_id: task.task_id,
                    title: task.title,
                    status: task.status
                  });
                  setSelectedTask(task.id);
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm text-muted-foreground">
                        {task.task_id}
                      </span>
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      <h3 className="font-semibold">{task.title}</h3>
                    </div>
                    
                    {task.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {task.description}
                      </p>
                    )}

                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      {task.assigned_to_profile && (
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>
                            {task.assigned_to_profile.first_name} {task.assigned_to_profile.last_name}
                          </span>
                        </div>
                      )}
                      
                      {task.due_date && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{format(new Date(task.due_date), 'MMM dd')}</span>
                        </div>
                      )}
                      
                      {task.estimated_hours && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{task.estimated_hours}h</span>
                        </div>
                      )}

                      {task.milestone?.name && (
                        <div className="flex items-center gap-1">
                          <span>Milestone: {task.milestone.name}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Badge 
                      className={getPriorityColor(task.priority)} 
                      variant="secondary"
                    >
                      {task.priority}
                    </Badge>
                    
                    <Select 
                      value={task.status} 
                      onValueChange={(value) => handleStatusChange(task.id, value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="todo">Todo</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="on_hold">On Hold</SelectItem>
                      </SelectContent>
                    </Select>

                    {task.assigned_to_profile && (
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="text-xs">
                          {getInitials(
                            task.assigned_to_profile.first_name,
                            task.assigned_to_profile.last_name
                          )}
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};