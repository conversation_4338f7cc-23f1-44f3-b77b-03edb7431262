# Frontend Integration Debug Guide

## Issue: "Task Not Found" Error

### Root Cause Analysis

The task **DOES exist** in the database and the user **DOES have access**. The issue is in the frontend integration.

### Database Verification ✅
- Task ID: `1fe05a6a-fa7b-4dd0-a2e6-5712bd8c265d`
- Task Code: `QU001-T001`
- Title: "Config- Org Structure"
- User has admin access ✅
- User created the task ✅
- User created the project ✅

### Frontend Debug Steps

#### Step 1: Check Browser Console
1. Open browser developer tools (F12)
2. Go to Console tab
3. Click on the task
4. Look for error messages

**Expected Console Logs:**
```
TaskDetailView: Fetching task with ID: 1fe05a6a-fa7b-4dd0-a2e6-5712bd8c265d
```

**Possible Error Messages:**
```
Task query failed: [error details]
```

#### Step 2: Check Network Tab
1. Open Network tab in developer tools
2. Click on the task
3. Look for failed API requests to Supabase

**Expected Request:**
```
POST https://rgcxrksmdkzarcdlscpk.supabase.co/rest/v1/tasks?select=...
```

#### Step 3: Check Component State
Add this debug code to `ProjectTasks.tsx`:

```typescript
// Add this after line 200 in ProjectTasks.tsx
onClick={() => {
  console.log('Task clicked:', task.id, task.task_id, task.title);
  setSelectedTask(task.id);
}}
```

#### Step 4: Check TaskDetailView Query
Add this debug code to `TaskDetailView.tsx`:

```typescript
// Add this after line 58 in TaskDetailView.tsx
console.log('TaskDetailView query result:', { task, isLoading, error });
```

### Most Likely Issues

#### Issue 1: Supabase Client Configuration
**Problem**: The Supabase client might not be properly authenticated.

**Fix**: Check if user is logged in:
```typescript
// Add to TaskDetailView.tsx
const { data: user } = useQuery(['current-user'], async () => {
  const { data: { user } } = await supabase.auth.getUser();
  console.log('Current user:', user);
  return user;
});
```

#### Issue 2: Query Syntax Error
**Problem**: The complex join query in TaskDetailView might be failing.

**Fix**: Simplify the query temporarily:
```typescript
// Replace the complex query in TaskDetailView.tsx with:
const { data: task, isLoading, error } = useQuery({
  queryKey: ['task', taskId],
  queryFn: async () => {
    console.log('Fetching task with ID:', taskId);
    
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('id', taskId)
      .single();
    
    console.log('Task query result:', { data, error });
    
    if (error) {
      console.error('Task query error:', error);
      throw error;
    }
    
    return data;
  },
});
```

#### Issue 3: RLS Policy in Frontend Context
**Problem**: RLS policies work differently when called from frontend vs direct SQL.

**Fix**: Test with RLS bypass (temporarily):
```typescript
// Add this test query to see if RLS is the issue
const testQuery = async () => {
  const { data, error } = await supabase
    .rpc('get_task_by_id', { task_uuid: taskId });
  console.log('RPC result:', { data, error });
};
```

#### Issue 4: React Query Cache Issue
**Problem**: Stale cache or query key conflicts.

**Fix**: Clear cache and retry:
```typescript
// Add to TaskDetailView.tsx
const queryClient = useQueryClient();

// Clear cache before query
useEffect(() => {
  queryClient.removeQueries(['task', taskId]);
}, [taskId, queryClient]);
```

### Quick Fix Implementation

#### 1. Add Debug Logging
Update `TaskDetailView.tsx`:

```typescript
export const TaskDetailView: React.FC<TaskDetailViewProps> = ({ taskId, onClose }) => {
  console.log('TaskDetailView mounted with taskId:', taskId);
  
  // Add debug query first
  const { data: debugTask } = useQuery({
    queryKey: ['debug-task', taskId],
    queryFn: async () => {
      console.log('Debug: Fetching task with simple query');
      const { data, error } = await supabase
        .from('tasks')
        .select('id, task_id, title, status')
        .eq('id', taskId)
        .single();
      
      console.log('Debug query result:', { data, error });
      return data;
    },
  });

  // Original complex query
  const { data: task, isLoading, error } = useQuery({
    queryKey: ['task', taskId],
    queryFn: async () => {
      console.log('Main query: Fetching task with complex joins');
      // ... existing query code
    },
  });

  console.log('TaskDetailView state:', { task, isLoading, error, debugTask });
  
  // ... rest of component
};
```

#### 2. Add Error Boundary
Create `ErrorBoundary.tsx`:

```typescript
import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('TaskDetailView Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 text-center">
          <h3 className="text-lg font-medium mb-2">Something went wrong</h3>
          <p className="text-muted-foreground">{this.state.error?.message}</p>
          <button onClick={() => this.setState({ hasError: false, error: null })}>
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Wrap TaskDetailView in ErrorBoundary
```

#### 3. Test with Direct Database Function
Create a simple RPC function for testing:

```sql
-- Add this function to test direct access
CREATE OR REPLACE FUNCTION public.get_task_debug(task_uuid uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  SELECT to_json(t.*) INTO result
  FROM public.tasks t
  WHERE t.id = task_uuid;
  
  RETURN result;
END;
$$;
```

Then test in frontend:
```typescript
const { data: rpcTask } = useQuery(['rpc-task', taskId], async () => {
  const { data, error } = await supabase.rpc('get_task_debug', { task_uuid: taskId });
  console.log('RPC task result:', { data, error });
  return data;
});
```

### Expected Resolution

After adding debug logging, you should see one of these patterns:

1. **Authentication Issue**: No user logged in
2. **Query Syntax Issue**: Specific SQL error in console
3. **RLS Issue**: Empty result despite user having access
4. **Component State Issue**: Task loads but UI doesn't update
5. **Network Issue**: Request fails or times out

### Next Steps

1. Add the debug logging code
2. Click on the task again
3. Check browser console for specific error messages
4. Report back the exact error messages you see

This will help identify the specific integration point that's failing.
